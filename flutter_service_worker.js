'use strict';
const MANIFEST = 'flutter-app-manifest';
const TEMP = 'flutter-temp-cache';
const CACHE_NAME = 'flutter-app-cache';

const RESOURCES = {"assets/AssetManifest.bin": "24218ac066e8ffd33acbd7dcf144ea4e",
"assets/AssetManifest.bin.json": "c121bb0ea08ccf68daa9a8cc306ebb8e",
"assets/AssetManifest.json": "e20477fbeaca8898aa51c80b7ba55b21",
"assets/assets/iconfont/iconfont.ttf": "c77b6fc7b89548527bf3d7bd6bff1b42",
"assets/assets/icons/normal.png": "7bb53b4f653e4382a78333d573803714",
"assets/assets/images/aliPay.png": "7b6b79f937680314f96e6aad84e29128",
"assets/assets/images/avatar.png": "422ed9149c1cae4906eff8c3026de82e",
"assets/assets/images/back10.png": "e39b5e9b4b58fcf80878d12934e22b07",
"assets/assets/images/back11.png": "6034710374d0a3239586978f3c828b12",
"assets/assets/images/back12.png": "1c8444062adf2cfea4317287814823d3",
"assets/assets/images/back13.png": "795a0126ab4ac10bd88c4111f9e62bc5",
"assets/assets/images/back14.png": "7fdbd0846d6cb2d56cb8d8b2c1a1619b",
"assets/assets/images/back15.png": "297ae65cb686184b6ed3b31eb1070c7d",
"assets/assets/images/back20.png": "cb628d721583806dc92012013beeb119",
"assets/assets/images/back21.png": "977db7a98f384b222143ea826ed457b9",
"assets/assets/images/back22.png": "8a69caebdfaacb11368f7319ec03852f",
"assets/assets/images/back23.png": "2c49f0791cfe3d7b1a351670f32df91a",
"assets/assets/images/back24.png": "da9afe7a187670f034e3d3324163fcf9",
"assets/assets/images/back25.png": "d30cecd53d6090fde843d41a60983085",
"assets/assets/images/banner.png": "23387dd595159f330835374dfa8c32c4",
"assets/assets/images/history_today.jpg": "fbb163682c283d67bc6eb860f0a55fc5",
"assets/assets/images/home_back.png": "d3f17ab77b65d06f8106782dae640970",
"assets/assets/images/home_back_new.png": "15c491b936d24340abd4dbf0a4bbab76",
"assets/assets/images/launcher_image_2_10_4.png": "4ba416699e853c00fc102311a8641787",
"assets/assets/images/menu_back.png": "d5694bb0dc7b7fee79b95ea12fe4ab17",
"assets/assets/images/raised_back.png": "f5e363bd5bb2d328d120bd0e96d46e0c",
"assets/assets/images/recommend.png": "cbd99e3f806e0365929f7636d7d236b0",
"assets/assets/images/resume.png": "4413f1afcdf0fe3dce12f27fb8709abc",
"assets/assets/images/spinner.gif": "cc38d043c2dd03942142cdf20886b067",
"assets/assets/images/title.png": "72f0c4147531f7121184b1502e76807e",
"assets/assets/images/title_back.png": "f297699ee567add77b6096db40b8f020",
"assets/assets/images/VIPB.png": "1aa6eb28bf49d5712d11e572e70f6bb3",
"assets/assets/images/VIPF.png": "808b0573338311306609d695c63bdbc6",
"assets/assets/images/VIPP.png": "c08425c6f5daebdb42fc3911c8fbee88",
"assets/assets/images/VIPT.png": "e7e3c76b5e149bca36413f0fbe93ee19",
"assets/assets/images/wechatPay.png": "51fb8be87ab63be115356bfd5243b1c9",
"assets/FontManifest.json": "a4750065fe8791de292f19926621059f",
"assets/fonts/MaterialIcons-Regular.otf": "49baa91ab5a6b7fddbb86027c4ee907f",
"assets/google_font.css": "03fb518fe1e1c4278be9ca12dc25a9e2",
"assets/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG9_FnY9NavzT.woff": "5ac554feb5a48517a0021c2defdf5545",
"assets/NOTICES": "9d343d04faebebacbea4cd9c99a65ad3",
"assets/notosanssc/v36/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG9_FnYxNbPzS5HE.ttf": "19498325f22381f551df06ce1b4cad4f",
"assets/packages/cupertino_icons/assets/CupertinoIcons.ttf": "e986ebe42ef785b27164c36a9abc7818",
"assets/packages/fluttertoast/assets/toastify.css": "a85675050054f179444bc5ad70ffc635",
"assets/packages/fluttertoast/assets/toastify.js": "56e2c9cedd97f10e7e5f1cebd85d53e3",
"assets/packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf": "4769f3245a24c1fa9965f113ea85ec2a",
"assets/packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf": "3ca5dc7621921b901d513cc1ce23788c",
"assets/packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf": "c3623a2c8dcde9278c4af3c364ef7a20",
"assets/packages/syncfusion_flutter_pdfviewer/assets/fonts/RobotoMono-Regular.ttf": "5b04fdfec4c8c36e8ca574e40b7148bb",
"assets/packages/syncfusion_flutter_pdfviewer/assets/highlight.png": "7384946432b51b56b0990dca1a735169",
"assets/packages/syncfusion_flutter_pdfviewer/assets/squiggly.png": "c9602bfd4aa99590ca66ce212099885f",
"assets/packages/syncfusion_flutter_pdfviewer/assets/strikethrough.png": "cb39da11cd936bd01d1c5a911e429799",
"assets/packages/syncfusion_flutter_pdfviewer/assets/underline.png": "c94a4441e753e4744e2857f0c4359bf0",
"assets/packages/wakelock_web/assets/no_sleep.js": "7748a45cd593f33280669b29c2c8919a",
"assets/pdfjs/pdf.min.js": "b41a52be93ec4dcfb160b72f15f515d7",
"assets/pdfjs/pdf.worker.min.js": "4559a1e9789c99336a572ca5bdcd8eeb",
"assets/roboto/v20/KFOmCnqEu92Fr1Me5WZLCzYlKw.ttf": "********************************",
"assets/shaders/ink_sparkle.frag": "ecc85a2e95f5e9f53123dcaf8cb9b6ce",
"canvaskit/canvaskit.js": "c86fbd9e7b17accae76e5ad116583dc4",
"canvaskit/canvaskit.js.symbols": "38cba9233b92472a36ff011dc21c2c9f",
"canvaskit/canvaskit.wasm": "********************************",
"canvaskit/chromium/canvaskit.js": "43787ac5098c648979c27c13c6f804c3",
"canvaskit/chromium/canvaskit.js.symbols": "4525682ef039faeb11f24f37436dca06",
"canvaskit/chromium/canvaskit.wasm": "f5934e694f12929ed56a671617acd254",
"canvaskit/skwasm.js": "445e9e400085faead4493be2224d95aa",
"canvaskit/skwasm.js.symbols": "741d50ffba71f89345996b0aa8426af8",
"canvaskit/skwasm.wasm": "e42815763c5d05bba43f9d0337fa7d84",
"canvaskit/skwasm.worker.js": "bfb704a6c714a75da9ef320991e88b03",
"favicon.png": "e79e7a22374550853c699f14889e386a",
"flutter.js": "c71a09214cb6f5f8996a531350400a9a",
"icons/Icon-192.png": "e829214373c635ecad854a9eb749a256",
"icons/Icon-512.png": "b0966ec27e4cc2ffa0a71beb4c26f679",
"icons/Icon-maskable-192.png": "f3ebf8e297e35da541075a7693725711",
"icons/Icon-maskable-512.png": "e2faed09fdb012232266cb1c50434c37",
"index.html": "eb123d21c2dcd7d4a1ea383d82ab6c14",
"/": "eb123d21c2dcd7d4a1ea383d82ab6c14",
"main.dart.js": "2a573ba261aba929c5da1eed5e2d3a0c",
"manifest.json": "2b511cc9fa586eb8911fcac154a6570e",
"sqflite_sw.js": "b291f1a1b9194a2883bdc756a53a4fae",
"sqlite3.wasm": "f08450f1d5a088a01cec0eb541c3aeca",
"version.json": "c2e70acf24f1c19143de3ea6be6a0737"};
// The application shell files that are downloaded before a service worker can
// start.
const CORE = ["main.dart.js",
"index.html",
"assets/AssetManifest.bin.json",
"assets/FontManifest.json"];

// During install, the TEMP cache is populated with the application shell files.
self.addEventListener("install", (event) => {
  self.skipWaiting();
  return event.waitUntil(
    caches.open(TEMP).then((cache) => {
      return cache.addAll(
        CORE.map((value) => new Request(value, {'cache': 'reload'})));
    })
  );
});
// During activate, the cache is populated with the temp files downloaded in
// install. If this service worker is upgrading from one with a saved
// MANIFEST, then use this to retain unchanged resource files.
self.addEventListener("activate", function(event) {
  return event.waitUntil(async function() {
    try {
      var contentCache = await caches.open(CACHE_NAME);
      var tempCache = await caches.open(TEMP);
      var manifestCache = await caches.open(MANIFEST);
      var manifest = await manifestCache.match('manifest');
      // When there is no prior manifest, clear the entire cache.
      if (!manifest) {
        await caches.delete(CACHE_NAME);
        contentCache = await caches.open(CACHE_NAME);
        for (var request of await tempCache.keys()) {
          var response = await tempCache.match(request);
          await contentCache.put(request, response);
        }
        await caches.delete(TEMP);
        // Save the manifest to make future upgrades efficient.
        await manifestCache.put('manifest', new Response(JSON.stringify(RESOURCES)));
        // Claim client to enable caching on first launch
        self.clients.claim();
        return;
      }
      var oldManifest = await manifest.json();
      var origin = self.location.origin;
      for (var request of await contentCache.keys()) {
        var key = request.url.substring(origin.length + 1);
        if (key == "") {
          key = "/";
        }
        // If a resource from the old manifest is not in the new cache, or if
        // the MD5 sum has changed, delete it. Otherwise the resource is left
        // in the cache and can be reused by the new service worker.
        if (!RESOURCES[key] || RESOURCES[key] != oldManifest[key]) {
          await contentCache.delete(request);
        }
      }
      // Populate the cache with the app shell TEMP files, potentially overwriting
      // cache files preserved above.
      for (var request of await tempCache.keys()) {
        var response = await tempCache.match(request);
        await contentCache.put(request, response);
      }
      await caches.delete(TEMP);
      // Save the manifest to make future upgrades efficient.
      await manifestCache.put('manifest', new Response(JSON.stringify(RESOURCES)));
      // Claim client to enable caching on first launch
      self.clients.claim();
      return;
    } catch (err) {
      // On an unhandled exception the state of the cache cannot be guaranteed.
      console.error('Failed to upgrade service worker: ' + err);
      await caches.delete(CACHE_NAME);
      await caches.delete(TEMP);
      await caches.delete(MANIFEST);
    }
  }());
});
// The fetch handler redirects requests for RESOURCE files to the service
// worker cache.
self.addEventListener("fetch", (event) => {
  if (event.request.method !== 'GET') {
    return;
  }
  var origin = self.location.origin;
  var key = event.request.url.substring(origin.length + 1);
  // Redirect URLs to the index.html
  if (key.indexOf('?v=') != -1) {
    key = key.split('?v=')[0];
  }
  if (event.request.url == origin || event.request.url.startsWith(origin + '/#') || key == '') {
    key = '/';
  }
  // If the URL is not the RESOURCE list then return to signal that the
  // browser should take over.
  if (!RESOURCES[key]) {
    return;
  }
  // If the URL is the index.html, perform an online-first request.
  if (key == '/') {
    return onlineFirst(event);
  }
  event.respondWith(caches.open(CACHE_NAME)
    .then((cache) =>  {
      return cache.match(event.request).then((response) => {
        // Either respond with the cached resource, or perform a fetch and
        // lazily populate the cache only if the resource was successfully fetched.
        return response || fetch(event.request).then((response) => {
          if (response && Boolean(response.ok)) {
            cache.put(event.request, response.clone());
          }
          return response;
        });
      })
    })
  );
});
self.addEventListener('message', (event) => {
  // SkipWaiting can be used to immediately activate a waiting service worker.
  // This will also require a page refresh triggered by the main worker.
  if (event.data === 'skipWaiting') {
    self.skipWaiting();
    return;
  }
  if (event.data === 'downloadOffline') {
    downloadOffline();
    return;
  }
});
// Download offline will check the RESOURCES for all files not in the cache
// and populate them.
async function downloadOffline() {
  var resources = [];
  var contentCache = await caches.open(CACHE_NAME);
  var currentContent = {};
  for (var request of await contentCache.keys()) {
    var key = request.url.substring(origin.length + 1);
    if (key == "") {
      key = "/";
    }
    currentContent[key] = true;
  }
  for (var resourceKey of Object.keys(RESOURCES)) {
    if (!currentContent[resourceKey]) {
      resources.push(resourceKey);
    }
  }
  return contentCache.addAll(resources);
}
// Attempt to download the resource online before falling back to
// the offline cache.
function onlineFirst(event) {
  return event.respondWith(
    fetch(event.request).then((response) => {
      return caches.open(CACHE_NAME).then((cache) => {
        cache.put(event.request, response.clone());
        return response;
      });
    }).catch((error) => {
      return caches.open(CACHE_NAME).then((cache) => {
        return cache.match(event.request).then((response) => {
          if (response != null) {
            return response;
          }
          throw error;
        });
      });
    })
  );
}
