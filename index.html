<!DOCTYPE html>
<html>
  <head>
    <base href="/" />
    <meta charset="UTF-8" />
    <meta content="IE=Edge" http-equiv="X-UA-Compatible" />
    <meta name="description" content="数智化船员培训系统" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1,user-scalable=no"
    />
    <meta name="browsermode" content="application" />
    <meta name="x5-page-mode" content="app" />

    <!-- iOS meta tags & icons -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta
      name="apple-mobile-web-app-title"
      content="数智化船员培训系统"
    />
    <link rel="apple-touch-icon" href="icons/Icon-192.png" />

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="favicon.png" />

    <title>数智化船员培训系统</title>
    <link rel="manifest" href="manifest.json" />

    <style>
      body {
        margin: 0;
        padding: 0;
        height: 100vh;
        overflow: hidden;
        touch-action: none; /* 禁用所有触摸操作 */
      }
    </style>

    <script type="text/javascript">
      // The value below is injected by flutter build, do not touch.
      const serviceWorkerVersion = "2805644624";
    </script>
    <!-- This script adds the flutter initialization JS code -->
    <script type="text/javascript" src="flutter.js" defer></script>
  </head>
  <body style="touch-action: none">
    <table id="loading" style="width: 100vw; height: 100vh">
      <tr>
        <td align="center" valign="middle">
          <img
            src="/assets/assets/images/launcher_image_2_10_4.png"
            style="width: 100%"
          />
          <img
            src="/assets/assets/images/spinner.gif"
            style="
              width: 80px;
              height: 80px;
              position: absolute;
              left: calc(50% - 30px);
              bottom: calc(20% - 40px);
            "
          />
        </td>
      </tr>
    </table>
    <div id="flutter_app" style="display: flex; height: 100vh"></div>
    <script type="text/javascript">
      function isStandaloneApp() {
        try {
          const isStandalone =
            window.matchMedia("(display-mode: standalone)").matches ||
            window.navigator.standalone;
          const hasPWATag = new URLSearchParams(window.location.search).has(
            "pwa"
          );
          return isStandalone || hasPWATag;
        } catch (e) {
          return false;
        }
      }

      function identifyDevicePlatform() {
        var os = (function () {
          var ua = navigator.userAgent,
            isWindowsPhone = /(?:Windows Phone)/.test(ua),
            isSymbian = /(?:SymbianOS)/.test(ua) || isWindowsPhone,
            isAndroid = /(?:Android)/.test(ua),
            isFireFox = /(?:Firefox)/.test(ua),
            isChrome = /(?:Chrome|CriOS)/.test(ua),
            isTablet =
              /(?:iPad|PlayBook)/.test(ua) ||
              (isAndroid && !/(?:Mobile)/.test(ua)) ||
              (isFireFox && /(?:Tablet)/.test(ua)),
            isPhone = /(?:iPhone)/.test(ua) && !isTablet,
            isPc = !isPhone && !isAndroid && !isSymbian;
          return {
            isTablet: isTablet,
            isPhone: isPhone,
            isAndroid: isAndroid,
            isPc: isPc,
          };
        })();
        return os;
      }

      let deferredPrompt;

      window.addEventListener("beforeinstallprompt", (e) => {
        // 阻止浏览器默认的提示行为
        e.preventDefault();
        // 保存事件以便稍后使用
        deferredPrompt = e;
        // 显示自定义的提示
        showAddToHomeScreen();
      });

      setTimeout(() => {
        if (
          !deferredPrompt &&
          !isStandaloneApp() &&
          /Mobi|Android|iPhone/i.test(navigator.userAgent)
        ) {
          // 当前设备是移动设备，并且是非安装模式
          document.getElementById("flutter_app").style.height =
            "calc(100vh - 56px)";
        }
      }, 200);

      function showAddToHomeScreen() {
        if (!isStandaloneApp()) {
          // 当前设备是移动设备，并且是非安装模式
          document.getElementById("flutter_app").style.height =
            "calc(100vh - 56px)";

          // 创建一个按钮来触发添加到主屏幕的提示
          const addToHomeScreenButton = document.createElement("button");
          addToHomeScreenButton.textContent = "添加到桌面应用";
          addToHomeScreenButton.style.position = "fixed";
          addToHomeScreenButton.style.bottom = "70px";
          addToHomeScreenButton.style.left = "calc(50% - 80px)";
          addToHomeScreenButton.style.padding = "10px 20px";
          addToHomeScreenButton.style.backgroundColor = "#007bff";
          addToHomeScreenButton.style.color = "#fff";
          addToHomeScreenButton.style.border = "none";
          addToHomeScreenButton.style.borderRadius = "5px";
          addToHomeScreenButton.style.cursor = "pointer";

          addToHomeScreenButton.addEventListener("click", () => {
            addToHomeScreenButton.remove();
            // 显示添加到主屏幕的提示
            deferredPrompt.prompt();
            // 等待用户响应
            deferredPrompt.userChoice.then((choiceResult) => {
              if (choiceResult.outcome === "accepted") {
                console.log("User accepted the A2HS prompt");
              } else {
                console.log("User dismissed the A2HS prompt");
              }
              deferredPrompt = null;
            });
          });

          document.body.appendChild(addToHomeScreenButton);
          setTimeout(() => {
            addToHomeScreenButton.remove();
          }, 15000);
        }
      }

      window.addEventListener("appinstalled", (evt) => {
        console.log("App was installed", evt);
        // 可以在这里移除添加到主屏幕的按钮
        const addToHomeScreenButton = document.querySelector("button");
        if (addToHomeScreenButton) {
          addToHomeScreenButton.remove();
        }
      });

      window.addEventListener("load", function (ev) {
        // Download main.dart.js
        _flutter.loader.loadEntrypoint({
          serviceWorker: {
            serviceWorkerVersion: serviceWorkerVersion,
          },
          onEntrypointLoaded: function (engineInitializer) {
            engineInitializer
              .initializeEngine({
                hostElement: document.getElementById("flutter_app"),
                renderer: "canvaskit",
                canvasKitBaseUrl: "./canvaskit/",
              })
              .then(async function (appRunner) {
                startFontCheck();
                await appRunner.runApp();
                onResize();
              });
          },
        });
      });

      window.addEventListener("resize", onResize);

      function onResize() {
        var os = identifyDevicePlatform();
        if (os.isPc) {
          var width = window.innerWidth;
          var padding = (width - 720) / 2;
          if (padding > 0) {
            document.getElementById("flutter_app").style.marginLeft =
              padding + "px";
            document.getElementById("flutter_app").style.marginRight =
              padding + "px";
            document.getElementById("loading").style.paddingLeft =
              padding + "px";
            document.getElementById("loading").style.paddingRight =
              padding + "px";
          }
        }
      }

      document.body.addEventListener(
        "toucstart",
        function (event) {
          event.preventDefault();
        },
        { passive: false }
      );

      document.body.addEventListener(
        "touchmove",
        function (event) {
          event.preventDefault();
        },
        { passive: false }
      );

      onResize();

      // 配置参数
      const FONT_NAME = "Noto Sans SC"; // Canvaskit 实际使用的字体名
      const CHECK_INTERVAL = 500; // 检测间隔（毫秒）
      const TIMEOUT = 5000;      // 减少超时时间到5秒

      // 添加简单的备用方案：3秒后如果还在loading就直接显示
      setTimeout(() => {
        const loadingElement = document.getElementById("loading");
        if (loadingElement && loadingElement.style.display !== "none") {
          console.warn("强制显示应用（备用方案）");
          onFontLoaded();
        }
      }, 3000);

      // 启动检测
      let checkTimer = null;
      let timeoutTimer = null;

      function startFontCheck() {
        console.log("开始字体检测");

        // 添加备用方案：如果字体API不可用，直接显示应用
        if (!document.fonts || typeof document.fonts.check !== 'function') {
          console.warn("字体API不可用，直接显示应用");
          onFontLoaded();
          return;
        }

        checkTimer = setInterval(checkFontLoad, CHECK_INTERVAL);

        // 超时处理
        timeoutTimer = setTimeout(() => {
          clearInterval(checkTimer);
          console.error(`字体加载超时（${TIMEOUT}ms）`);
          onFontError();
        }, TIMEOUT);
      }

      function checkFontLoad(){
        try {
            // 添加调试信息
            console.log("正在检测字体:", FONT_NAME);
            console.log("可用字体:", Array.from(document.fonts).map(f => f.family));

            // 关键 API：检测字体是否加载完成
            const isLoaded = document.fonts.check("12px " + FONT_NAME);
            console.log("字体检测结果:", isLoaded);

            if (isLoaded) {
              console.log("字体加载成功");
              onFontLoaded();
            }
          } catch (error) {
            console.error("字体检测 API 异常:", error);
            onFontError();
          }
      }

      // 字体加载成功回调
      function onFontLoaded() {
        clearInterval(checkTimer);
        clearTimeout(timeoutTimer);
        // 隐藏提示层，显示应用
       document.getElementById("loading").style.display = "none";
      }

      // 字体加载失败回调
      function onFontError() {
        // 可选：显示错误提示或降级内容
        document.getElementById("loading").style.display = "none";
      }

      // 启动检测（DOM 就绪后执行）
      //document.addEventListener('DOMContentLoaded', startFontCheck);

    </script>

    <script src="/assets/pdfjs/pdf.min.js"></script>
    <script type="text/javascript">
      pdfjsLib.GlobalWorkerOptions.workerSrc =
        "/training/web/assets/pdfjs/pdf.worker.min.js";
    </script>
  </body>
</html>
